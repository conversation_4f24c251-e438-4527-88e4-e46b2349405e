import { DataTypes, Model } from 'sequelize';
import { userDatabase } from '../../config/database.js';
import { EncryptionUtil } from '../../utils/encryption.js';

/**
 * SubscriptionPlan Model
 * Manages subscription plan definitions and pricing
 */
class SubscriptionPlan extends Model {
  /**
   * Check if plan is free tier
   * @returns {boolean} True if plan is free
   */
  isFree() {
    return this.planType === 'EXPLORER';
  }

  /**
   * Check if plan is paid tier
   * @returns {boolean} True if plan is paid
   */
  isPaid() {
    return ['CREATOR', 'PRO'].includes(this.planType);
  }

  /**
   * Get plan features as object
   * @returns {Object} Parsed features object
   */
  getPlanFeatures() {
    try {
      return JSON.parse(this.features || '{}');
    } catch (error) {
      return {};
    }
  }

  /**
   * Get plan limits as object
   * @returns {Object} Parsed limits object
   */
  getPlanLimits() {
    try {
      return JSON.parse(this.limits || '{}');
    } catch (error) {
      return {};
    }
  }

  /**
   * Create a new subscription plan
   * @param {Object} planData - Plan data
   * @returns {Promise<SubscriptionPlan>} Created plan
   */
  static async createPlan(planData) {
    return SubscriptionPlan.create({
      id: EncryptionUtil.generateUUID(),
      ...planData,
    });
  }

  /**
   * Find plan by type
   * @param {string} planType - Plan type
   * @returns {Promise<SubscriptionPlan|null>} Plan or null
   */
  static async findByType(planType) {
    return SubscriptionPlan.findOne({
      where: { planType, isActive: true },
    });
  }

  /**
   * Get all active plans
   * @returns {Promise<Array>} Active plans
   */
  static async getActivePlans() {
    return SubscriptionPlan.findAll({
      where: { isActive: true },
      order: [['sortOrder', 'ASC']],
    });
  }
}

SubscriptionPlan.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    planType: {
      type: DataTypes.ENUM('EXPLORER', 'CREATOR', 'PRO', 'ADDON'),
      allowNull: false,
      unique: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    currency: {
      type: DataTypes.STRING(3),
      allowNull: false,
      defaultValue: 'INR',
    },
    billingCycle: {
      type: DataTypes.ENUM('WEEKLY', 'MONTHLY', 'YEARLY', 'ONE_TIME'),
      allowNull: false,
      defaultValue: 'MONTHLY',
    },
    credits: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    isUnlimitedCredits: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    features: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'JSON object containing plan features',
    },
    limits: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'JSON object containing plan limits (projects, files, etc.)',
    },
    razorpayPlanId: {
      type: DataTypes.STRING(255),
      allowNull: true,
      unique: true,
      comment: 'Razorpay plan ID for recurring payments',
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    sortOrder: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'SubscriptionPlan',
    tableName: 'subscription_plans',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['plan_type'],
      },
      {
        unique: true,
        fields: ['razorpay_plan_id'],
        where: {
          razorpay_plan_id: {
            [DataTypes.Op?.ne || '$ne']: null,
          },
        },
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['sort_order'],
      },
    ],
  }
);

export { SubscriptionPlan };
